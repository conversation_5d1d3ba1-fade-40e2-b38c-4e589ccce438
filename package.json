{"name": "escooter-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.0.3", "@tanstack/react-query": "^5.77.1", "axios": "^1.9.0", "framer-motion": "^12.4.0", "lucide-react": "^0.474.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.6.1", "recharts": "^2.15.1", "tailwindcss": "^4.0.3", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "vite": "^6.1.0"}}