// src/pages/Dashboard.jsx
import { useState } from "react";
import {
  Bike,
  DollarSign,
  Users,
  AlertTriangle,
  Activity,
  Battery
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { useTranslation } from "react-i18next";
import { scooterService, authService } from "../services/api";
import StatCard from "../components/dashboard/StatCard";
import RevenueChart from "../components/dashboard/RevenueChart";
import UsageHeatmap from "../components/dashboard/UsageHeatmap";
import RecentActivities from "../components/dashboard/RecentActivities";
import SystemAlerts from "../components/dashboard/SystemAlerts";
import FleetStatus from "../components/dashboard/FleetStatus";
import ApiTestPanel from "../components/dashboard/ApiTestPanel";

const Dashboard = () => {
  const { t } = useTranslation();
  const [timeRange, setTimeRange] = useState("24h");
  const [showApiPanel, setShowApiPanel] = useState(false);

  // Fetch scooter data
  const { data: scootersData, isLoading: isLoadingScooters } = useQuery({
    queryKey: ["scooters-summary"],
    queryFn: async () => {
      try {
        const response = await scooterService.getAllScooters();
        return response.data;
      } catch (error) {
        console.error("Error fetching scooters:", error);
        return [];
      }
    },
    refetchInterval: 30000, // Refetch every 30 seconds for real-time data
  });

  // Fetch rides data
  const { data: ridesData, isLoading: isLoadingRides } = useQuery({
    queryKey: ["rides-summary", timeRange],
    queryFn: async () => {
      try {
        const response = await scooterService.getAllRides();
        return response.data;
      } catch (error) {
        console.error("Error fetching rides:", error);
        return [];
      }
    },
    refetchInterval: 30000,
  });

  // Fetch users data
  const { data: usersData, isLoading: isLoadingUsers } = useQuery({
    queryKey: ["users-summary"],
    queryFn: async () => {
      try {
        const response = await authService.getAllUsers();
        return response.data;
      } catch (error) {
        console.error("Error fetching users:", error);
        return [];
      }
    },
    refetchInterval: 60000, // Refetch every minute
  });

  // Calculate enhanced stats
  const scootersCount = scootersData?.length || 0;
  const activeScooters = scootersData?.filter((s) => s.status === "available")?.length || 0;
  const inUseScooters = scootersData?.filter((s) => s.status === "in_use")?.length || 0;
  const maintenanceScooters = scootersData?.filter((s) => s.status === "maintenance")?.length || 0;
  const lowBatteryScooters = scootersData?.filter((s) => s.battery < 20)?.length || 0;

  const totalRides = ridesData?.length || 0;
  const activeRides = ridesData?.filter((r) => r.status === "active")?.length || 0;
  const totalUsers = usersData?.length || 0;
  const activeUsers = usersData?.filter((u) => u.status === "active")?.length || 0;

  // Calculate revenue with more sophisticated logic
  const revenue = ridesData?.reduce((total, ride) => {
    const duration = ride.duration || 15;
    const basePrice = 2;
    const perMinuteRate = 0.25;
    return total + (basePrice + duration * perMinuteRate);
  }, 0) || 0;



  // Enhanced stat cards data
  const stats = [
    {
      title: t("dashboard.totalRevenue"),
      value: isLoadingRides ? t("common.loading") : `$${revenue.toFixed(2)}`,
      icon: DollarSign,
      trend: 12.5,
      color: "green",
      subtitle: `${totalRides} ${t("dashboard.ridesCompleted")}`,
    },
    {
      title: t("dashboard.activeScooters"),
      value: isLoadingScooters ? t("common.loading") : `${activeScooters}/${scootersCount}`,
      icon: Bike,
      trend: -2.3,
      color: "blue",
      subtitle: `${inUseScooters} ${t("dashboard.currentlyInUse")}`,
    },
    {
      title: t("dashboard.activeUsers"),
      value: isLoadingUsers ? t("common.loading") : activeUsers.toString(),
      icon: Users,
      trend: 8.7,
      color: "purple",
      subtitle: `${totalUsers} ${t("dashboard.totalRegistered")}`,
    },
    {
      title: t("dashboard.activeRides"),
      value: isLoadingRides ? t("common.loading") : activeRides.toString(),
      icon: Activity,
      trend: 15.2,
      color: "orange",
      subtitle: t("dashboard.currentlyOngoing"),
    },
  ];

  // System alerts data
  const alerts = [
    ...(lowBatteryScooters > 0 ? [{
      id: 1,
      type: "warning",
      title: "Low Battery Alert",
      message: `${lowBatteryScooters} scooters have battery below 20%`,
      time: "2 minutes ago",
      icon: Battery,
    }] : []),
    ...(maintenanceScooters > 0 ? [{
      id: 2,
      type: "info",
      title: "Maintenance Required",
      message: `${maintenanceScooters} scooters require maintenance`,
      time: "5 minutes ago",
      icon: AlertTriangle,
    }] : []),
  ];

  return (
    <div className="space-y-6">
      {/* Header with time range selector */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">{t("dashboard.title")}</h1>
          <p className="text-gray-600">{t("dashboard.subtitle")}</p>
        </div>
        <div className="flex items-center space-x-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500"
          >
            <option value="1h">{t("dashboard.lastHour")}</option>
            <option value="24h">{t("dashboard.last24Hours")}</option>
            <option value="7d">{t("dashboard.last7Days")}</option>
            <option value="30d">{t("dashboard.last30Days")}</option>
          </select>
          <button
            onClick={() => setShowApiPanel(!showApiPanel)}
            className="px-3 py-2 text-sm bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200"
          >
            {showApiPanel ? t("dashboard.hideApiPanel") : t("dashboard.showApiPanel")}
          </button>
        </div>
      </div>

      {/* Enhanced stat cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <StatCard key={index} {...stat} />
        ))}
      </div>

      {/* Charts and Analytics Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <RevenueChart timeRange={timeRange} />
        <UsageHeatmap />
      </div>

      {/* Fleet Status and Alerts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <FleetStatus scooters={scootersData || []} />
        </div>
        <SystemAlerts alerts={alerts} />
      </div>

      {/* Recent Activities */}
      <RecentActivities />

      {/* API Testing Panel for Development */}
      {showApiPanel && <ApiTestPanel />}
    </div>
  );
};

export default Dashboard;
