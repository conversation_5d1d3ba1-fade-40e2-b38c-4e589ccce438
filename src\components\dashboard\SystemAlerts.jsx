// src/components/dashboard/SystemAlerts.jsx
import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  AlertTriangle,
  AlertCircle,
  Info,
  CheckCircle,
  X,
  Bell,
  Clock,
  Filter,
} from "lucide-react";

const SystemAlerts = ({ alerts = [] }) => {
  const [filter, setFilter] = useState("all"); // all, warning, error, info, success
  const [dismissedAlerts, setDismissedAlerts] = useState(new Set());

  // Mock additional alerts if none provided
  const defaultAlerts = [
    {
      id: 1,
      type: "warning",
      title: "Low Battery Alert",
      message: "5 scooters have battery below 20%",
      time: "2 minutes ago",
      icon: AlertTriangle,
      actionable: true,
    },
    {
      id: 2,
      type: "error",
      title: "Scooter Offline",
      message: "Scooter SC-001 has been offline for 30 minutes",
      time: "5 minutes ago",
      icon: AlertCircle,
      actionable: true,
    },
    {
      id: 3,
      type: "info",
      title: "Maintenance Scheduled",
      message: "3 scooters scheduled for maintenance tomorrow",
      time: "10 minutes ago",
      icon: Info,
      actionable: false,
    },
    {
      id: 4,
      type: "success",
      title: "Fleet Update Complete",
      message: "Software update deployed to 25 scooters",
      time: "15 minutes ago",
      icon: CheckCircle,
      actionable: false,
    },
  ];

  const allAlerts = alerts.length > 0 ? alerts : defaultAlerts;
  
  const filteredAlerts = allAlerts.filter((alert) => {
    if (dismissedAlerts.has(alert.id)) return false;
    if (filter === "all") return true;
    return alert.type === filter;
  });

  const getAlertStyles = (type) => {
    switch (type) {
      case "error":
        return {
          bg: "bg-red-50",
          border: "border-red-200",
          text: "text-red-800",
          icon: "text-red-600",
        };
      case "warning":
        return {
          bg: "bg-yellow-50",
          border: "border-yellow-200",
          text: "text-yellow-800",
          icon: "text-yellow-600",
        };
      case "info":
        return {
          bg: "bg-blue-50",
          border: "border-blue-200",
          text: "text-blue-800",
          icon: "text-blue-600",
        };
      case "success":
        return {
          bg: "bg-green-50",
          border: "border-green-200",
          text: "text-green-800",
          icon: "text-green-600",
        };
      default:
        return {
          bg: "bg-gray-50",
          border: "border-gray-200",
          text: "text-gray-800",
          icon: "text-gray-600",
        };
    }
  };

  const dismissAlert = (alertId) => {
    setDismissedAlerts(prev => new Set([...prev, alertId]));
  };

  const getFilterCount = (type) => {
    if (type === "all") return allAlerts.filter(a => !dismissedAlerts.has(a.id)).length;
    return allAlerts.filter(a => a.type === type && !dismissedAlerts.has(a.id)).length;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
    >
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <Bell className="h-5 w-5 mr-2 text-orange-600" />
            System Alerts
          </h3>
          <p className="text-sm text-gray-600">
            {filteredAlerts.length} active alerts
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-2 focus:ring-orange-500"
          >
            <option value="all">All ({getFilterCount("all")})</option>
            <option value="error">Errors ({getFilterCount("error")})</option>
            <option value="warning">Warnings ({getFilterCount("warning")})</option>
            <option value="info">Info ({getFilterCount("info")})</option>
            <option value="success">Success ({getFilterCount("success")})</option>
          </select>
        </div>
      </div>

      <div className="space-y-3 max-h-80 overflow-y-auto">
        <AnimatePresence>
          {filteredAlerts.length === 0 ? (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="text-center py-8"
            >
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-3" />
              <p className="text-gray-600">No alerts to display</p>
              <p className="text-sm text-gray-500">All systems running smoothly</p>
            </motion.div>
          ) : (
            filteredAlerts.map((alert, index) => {
              const styles = getAlertStyles(alert.type);
              const IconComponent = alert.icon;

              return (
                <motion.div
                  key={alert.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ delay: index * 0.1 }}
                  className={`p-4 rounded-lg border ${styles.bg} ${styles.border} relative group`}
                >
                  <div className="flex items-start space-x-3">
                    <IconComponent className={`h-5 w-5 mt-0.5 ${styles.icon} flex-shrink-0`} />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h4 className={`text-sm font-medium ${styles.text}`}>
                          {alert.title}
                        </h4>
                        <button
                          onClick={() => dismissAlert(alert.id)}
                          className="opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-white hover:bg-opacity-50 rounded"
                        >
                          <X className="h-4 w-4 text-gray-500" />
                        </button>
                      </div>
                      <p className={`text-sm ${styles.text} opacity-80 mt-1`}>
                        {alert.message}
                      </p>
                      <div className="flex items-center justify-between mt-2">
                        <div className="flex items-center text-xs text-gray-500">
                          <Clock className="h-3 w-3 mr-1" />
                          {alert.time}
                        </div>
                        {alert.actionable && (
                          <button className={`text-xs font-medium ${styles.text} hover:underline`}>
                            Take Action
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </motion.div>
              );
            })
          )}
        </AnimatePresence>
      </div>

      {filteredAlerts.length > 0 && (
        <div className="mt-4 pt-4 border-t border-gray-100">
          <div className="flex items-center justify-between">
            <button className="text-sm text-gray-600 hover:text-gray-800">
              Mark all as read
            </button>
            <button className="text-sm text-orange-600 hover:text-orange-800 font-medium">
              View all alerts
            </button>
          </div>
        </div>
      )}
    </motion.div>
  );
};

export default SystemAlerts;
