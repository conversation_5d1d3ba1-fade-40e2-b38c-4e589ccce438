// src/components/notifications/NotificationStats.jsx
import React from "react";
import { motion } from "framer-motion";
import { Target, TrendingUp } from "lucide-react";

const NotificationStats = () => {
  return (
    <div className="space-y-6">
      <div className="text-center py-12">
        <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Notification Analytics</h3>
        <p className="text-gray-600">
          Detailed analytics and performance metrics for your notification campaigns
        </p>
      </div>
    </div>
  );
};

export default NotificationStats;
