// src/pages/Geofencing.jsx
import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  MapPin,
  Plus,
  Edit,
  Trash2,
  Shield,
  AlertTriangle,
  Clock,
  Users,
  Activity,
  Settings,
  Eye,
  EyeOff
} from "lucide-react";

const Geofencing = () => {
  const [activeTab, setActiveTab] = useState("zones");
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedZone, setSelectedZone] = useState(null);

  // Mock geofencing zones data
  const geofenceZones = [
    {
      id: 1,
      name: "Downtown Business District",
      type: "allowed",
      coordinates: "40.7128,-74.0060",
      radius: 500,
      rules: {
        maxSpeed: 15,
        parkingAllowed: true,
        ridingAllowed: true,
        timeRestrictions: "24/7"
      },
      status: "active",
      scootersInside: 12,
      createdAt: "2024-01-15",
      description: "Main business area with full scooter access"
    },
    {
      id: 2,
      name: "University Campus",
      type: "restricted",
      coordinates: "40.7589,-73.9851",
      radius: 300,
      rules: {
        maxSpeed: 8,
        parkingAllowed: false,
        ridingAllowed: true,
        timeRestrictions: "6:00-22:00"
      },
      status: "active",
      scootersInside: 5,
      createdAt: "2024-01-10",
      description: "Reduced speed zone during campus hours"
    },
    {
      id: 3,
      name: "Hospital Zone",
      type: "no-ride",
      coordinates: "40.7505,-73.9934",
      radius: 200,
      rules: {
        maxSpeed: 0,
        parkingAllowed: false,
        ridingAllowed: false,
        timeRestrictions: "24/7"
      },
      status: "active",
      scootersInside: 0,
      createdAt: "2024-01-08",
      description: "No scooter access near medical facilities"
    },
    {
      id: 4,
      name: "Waterfront Park",
      type: "parking-only",
      coordinates: "40.7614,-73.9776",
      radius: 150,
      rules: {
        maxSpeed: 0,
        parkingAllowed: true,
        ridingAllowed: false,
        timeRestrictions: "24/7"
      },
      status: "inactive",
      scootersInside: 3,
      createdAt: "2024-01-05",
      description: "Designated parking area only"
    }
  ];

  const getZoneTypeColor = (type) => {
    switch (type) {
      case "allowed":
        return "bg-green-100 text-green-800";
      case "restricted":
        return "bg-yellow-100 text-yellow-800";
      case "no-ride":
        return "bg-red-100 text-red-800";
      case "parking-only":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (status) => {
    return status === "active"
      ? "bg-green-100 text-green-800"
      : "bg-gray-100 text-gray-800";
  };

  const tabs = [
    { id: "zones", label: "Geofence Zones", count: geofenceZones.length },
    { id: "map", label: "Interactive Map", count: null },
    { id: "analytics", label: "Zone Analytics", count: null },
    { id: "settings", label: "Global Settings", count: null }
  ];

  const ZoneForm = () => {
    const [formData, setFormData] = useState({
      name: "",
      type: "allowed",
      coordinates: "",
      radius: 100,
      maxSpeed: 15,
      parkingAllowed: true,
      ridingAllowed: true,
      timeRestrictions: "24/7",
      description: "",
      assignedScooters: ""
    });

    const handleSubmit = (e) => {
      e.preventDefault();

      // Process assigned scooters - split by comma and clean up
      const scooterList = formData.assignedScooters
        .split(',')
        .map(scooter => scooter.trim())
        .filter(scooter => scooter.length > 0);

      const zoneData = {
        ...formData,
        assignedScooters: scooterList
      };

      console.log("Creating zone:", zoneData);
      setShowCreateForm(false);
    };

    return (
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      >
        <motion.div
          initial={{ scale: 0.9, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0.9, opacity: 0 }}
          className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto"
        >
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Create Geofence Zone</h3>
          </div>

          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Zone Name
                </label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="Enter zone name"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Zone Type
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData({...formData, type: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <option value="allowed">Allowed Zone</option>
                  <option value="restricted">Restricted Zone</option>
                  <option value="no-ride">No-Ride Zone</option>
                  <option value="parking-only">Parking Only</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Coordinates (Lat, Lng)
                </label>
                <input
                  type="text"
                  value={formData.coordinates}
                  onChange={(e) => setFormData({...formData, coordinates: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="40.7128,-74.0060"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Radius (meters)
                </label>
                <input
                  type="number"
                  value={formData.radius}
                  onChange={(e) => setFormData({...formData, radius: parseInt(e.target.value)})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                  min="50"
                  max="2000"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Max Speed (km/h)
                </label>
                <input
                  type="number"
                  value={formData.maxSpeed}
                  onChange={(e) => setFormData({...formData, maxSpeed: parseInt(e.target.value)})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                  min="0"
                  max="25"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Time Restrictions
                </label>
                <input
                  type="text"
                  value={formData.timeRestrictions}
                  onChange={(e) => setFormData({...formData, timeRestrictions: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="24/7 or 6:00-22:00"
                />
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center space-x-6">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.parkingAllowed}
                    onChange={(e) => setFormData({...formData, parkingAllowed: e.target.checked})}
                    className="mr-2"
                  />
                  Parking Allowed
                </label>
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.ridingAllowed}
                    onChange={(e) => setFormData({...formData, ridingAllowed: e.target.checked})}
                    className="mr-2"
                  />
                  Riding Allowed
                </label>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                  rows="3"
                  placeholder="Zone description and additional rules"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Assigned Scooters (Optional)
                </label>
                <input
                  type="text"
                  value={formData.assignedScooters}
                  onChange={(e) => setFormData({...formData, assignedScooters: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="Enter scooter IDs or tags separated by commas (e.g., SC-001, SC-002, tag:downtown)"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Specify individual scooter IDs (SC-001) or use tags (tag:region-name) to assign scooters to this zone
                </p>
              </div>
            </div>

            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => setShowCreateForm(false)}
                className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
              >
                Create Zone
              </button>
            </div>
          </form>
        </motion.div>
      </motion.div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <MapPin className="h-6 w-6 mr-3 text-green-600" />
            Geofencing Management
          </h1>
          <p className="text-gray-600 mt-1">
            Define and manage geofenced areas for your e-scooter operations
          </p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
        >
          <Plus className="h-4 w-4 mr-2" />
          Create Zone
        </button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Zones</p>
              <p className="text-2xl font-bold text-green-600">{geofenceZones.length}</p>
              <p className="text-xs text-green-600 mt-1">
                {geofenceZones.filter(z => z.status === 'active').length} active
              </p>
            </div>
            <Shield className="h-8 w-8 text-green-600" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Scooters in Zones</p>
              <p className="text-2xl font-bold text-blue-600">
                {geofenceZones.reduce((sum, zone) => sum + zone.scootersInside, 0)}
              </p>
              <p className="text-xs text-blue-600 mt-1">Currently tracked</p>
            </div>
            <Activity className="h-8 w-8 text-blue-600" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Restricted Zones</p>
              <p className="text-2xl font-bold text-red-600">
                {geofenceZones.filter(z => z.type === 'no-ride' || z.type === 'restricted').length}
              </p>
              <p className="text-xs text-red-600 mt-1">Active restrictions</p>
            </div>
            <AlertTriangle className="h-8 w-8 text-red-600" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Parking Zones</p>
              <p className="text-2xl font-bold text-purple-600">
                {geofenceZones.filter(z => z.rules.parkingAllowed).length}
              </p>
              <p className="text-xs text-purple-600 mt-1">Parking allowed</p>
            </div>
            <MapPin className="h-8 w-8 text-purple-600" />
          </div>
        </motion.div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? "border-green-500 text-green-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                {tab.label}
                {tab.count !== null && (
                  <span className="ml-2 bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === "zones" && (
            <div className="space-y-4">
              {geofenceZones.map((zone, index) => (
                <motion.div
                  key={zone.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-3">
                        <h3 className="text-lg font-semibold text-gray-900">{zone.name}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getZoneTypeColor(zone.type)}`}>
                          {zone.type.replace('-', ' ')}
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(zone.status)}`}>
                          {zone.status}
                        </span>
                      </div>

                      <p className="text-sm text-gray-600 mb-4">{zone.description}</p>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="font-medium text-gray-700">Coordinates:</span>
                          <p className="text-gray-600">{zone.coordinates}</p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Radius:</span>
                          <p className="text-gray-600">{zone.radius}m</p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Max Speed:</span>
                          <p className="text-gray-600">{zone.rules.maxSpeed} km/h</p>
                        </div>
                        <div>
                          <span className="font-medium text-gray-700">Scooters Inside:</span>
                          <p className="text-gray-600">{zone.scootersInside}</p>
                        </div>
                      </div>

                      <div className="mt-4 flex items-center space-x-6 text-sm">
                        <div className="flex items-center">
                          <span className="font-medium text-gray-700 mr-2">Rules:</span>
                          <div className="flex space-x-3">
                            <span className={`px-2 py-1 rounded text-xs ${zone.rules.parkingAllowed ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                              Parking {zone.rules.parkingAllowed ? 'Allowed' : 'Forbidden'}
                            </span>
                            <span className={`px-2 py-1 rounded text-xs ${zone.rules.ridingAllowed ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                              Riding {zone.rules.ridingAllowed ? 'Allowed' : 'Forbidden'}
                            </span>
                            <span className="px-2 py-1 rounded text-xs bg-blue-100 text-blue-800">
                              {zone.rules.timeRestrictions}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 ml-6">
                      <button className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                        <Eye className="h-4 w-4" />
                      </button>
                      <button className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                        <Edit className="h-4 w-4" />
                      </button>
                      <button
                        className="p-2 text-gray-600 hover:text-yellow-600 hover:bg-yellow-50 rounded-lg transition-colors"
                        title={zone.status === 'active' ? 'Deactivate' : 'Activate'}
                      >
                        {zone.status === 'active' ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                      <button className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors">
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {activeTab === "map" && (
            <div className="h-96 bg-gray-100 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Interactive Geofencing Map</h3>
                <p className="text-gray-600">
                  Real-time map view showing all geofenced zones and scooter locations
                </p>
                <p className="text-sm text-gray-500 mt-2">
                  Map integration with zone boundaries, scooter tracking, and zone management tools
                </p>
              </div>
            </div>
          )}

          {activeTab === "analytics" && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-gray-50 p-6 rounded-lg">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Zone Violations</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Speed violations</span>
                      <span className="font-medium">23 this week</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Parking violations</span>
                      <span className="font-medium">12 this week</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">No-ride zone entries</span>
                      <span className="font-medium">5 this week</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 p-6 rounded-lg">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4">Zone Usage</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Most active zone</span>
                      <span className="font-medium">Downtown Business</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Average dwell time</span>
                      <span className="font-medium">15 minutes</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Peak usage hours</span>
                      <span className="font-medium">8-10 AM, 5-7 PM</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === "settings" && (
            <div className="space-y-6">
              <div className="bg-gray-50 p-6 rounded-lg">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">Global Geofencing Settings</h4>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Enable Geofencing</label>
                      <p className="text-xs text-gray-500">Master switch for all geofencing features</p>
                    </div>
                    <input type="checkbox" defaultChecked className="toggle" />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Automatic Speed Limiting</label>
                      <p className="text-xs text-gray-500">Automatically limit speed in restricted zones</p>
                    </div>
                    <input type="checkbox" defaultChecked className="toggle" />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-gray-700">Zone Violation Alerts</label>
                      <p className="text-xs text-gray-500">Send alerts when scooters violate zone rules</p>
                    </div>
                    <input type="checkbox" defaultChecked className="toggle" />
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Create Zone Form Modal */}
      <AnimatePresence>
        {showCreateForm && <ZoneForm />}
      </AnimatePresence>
    </div>
  );
};

export default Geofencing;