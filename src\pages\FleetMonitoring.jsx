// src/pages/FleetMonitoring.jsx
import React, { useState } from "react";
import { motion } from "framer-motion";
import {
  Activity,
  MapPin,
  Battery,
  Zap,
  AlertTriangle,
  CheckCircle,
  Navigation,
  Clock,
  Filter,
  RefreshCw,
} from "lucide-react";

const FleetMonitoring = () => {
  const [viewMode, setViewMode] = useState("grid"); // grid, map, list
  const [filterStatus, setFilterStatus] = useState("all");
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Mock real-time fleet data
  const fleetData = [
    {
      id: "SC-001",
      status: "in_use",
      battery: 78,
      location: { name: "Downtown Plaza", lat: 40.7128, lng: -74.0060 },
      speed: 15,
      rider: "John <PERSON>",
      rideTime: "12 min",
      lastUpdate: "2 sec ago",
      health: "good",
    },
    {
      id: "SC-002",
      status: "available",
      battery: 92,
      location: { name: "University Campus", lat: 40.7589, lng: -73.9851 },
      speed: 0,
      rider: null,
      rideTime: null,
      lastUpdate: "5 sec ago",
      health: "excellent",
    },
    {
      id: "SC-003",
      status: "charging",
      battery: 45,
      location: { name: "Charging Station A", lat: 40.7505, lng: -73.9934 },
      speed: 0,
      rider: null,
      rideTime: null,
      lastUpdate: "1 min ago",
      health: "good",
    },
    {
      id: "SC-004",
      status: "maintenance",
      battery: 15,
      location: { name: "Service Center", lat: 40.7614, lng: -73.9776 },
      speed: 0,
      rider: null,
      rideTime: null,
      lastUpdate: "30 min ago",
      health: "poor",
    },
    {
      id: "SC-005",
      status: "offline",
      battery: 0,
      location: { name: "Unknown", lat: null, lng: null },
      speed: 0,
      rider: null,
      rideTime: null,
      lastUpdate: "2 hours ago",
      health: "critical",
    },
  ];

  const getStatusIcon = (status) => {
    switch (status) {
      case "in_use":
        return <Navigation className="h-4 w-4 text-blue-600" />;
      case "available":
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "charging":
        return <Zap className="h-4 w-4 text-yellow-600" />;
      case "maintenance":
        return <AlertTriangle className="h-4 w-4 text-orange-600" />;
      case "offline":
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default:
        return <Activity className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "in_use":
        return "bg-blue-100 text-blue-800";
      case "available":
        return "bg-green-100 text-green-800";
      case "charging":
        return "bg-yellow-100 text-yellow-800";
      case "maintenance":
        return "bg-orange-100 text-orange-800";
      case "offline":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getBatteryColor = (battery) => {
    if (battery >= 60) return "text-green-600";
    if (battery >= 30) return "text-yellow-600";
    return "text-red-600";
  };

  const getHealthColor = (health) => {
    switch (health) {
      case "excellent":
        return "text-green-600";
      case "good":
        return "text-blue-600";
      case "fair":
        return "text-yellow-600";
      case "poor":
        return "text-orange-600";
      case "critical":
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  const filteredFleet = fleetData.filter((scooter) => {
    if (filterStatus === "all") return true;
    return scooter.status === filterStatus;
  });

  const stats = {
    total: fleetData.length,
    active: fleetData.filter((s) => s.status === "in_use").length,
    available: fleetData.filter((s) => s.status === "available").length,
    charging: fleetData.filter((s) => s.status === "charging").length,
    maintenance: fleetData.filter((s) => s.status === "maintenance").length,
    offline: fleetData.filter((s) => s.status === "offline").length,
    lowBattery: fleetData.filter((s) => s.battery < 20).length,
    avgBattery: Math.round(
      fleetData.reduce((sum, s) => sum + s.battery, 0) / fleetData.length
    ),
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <Activity className="h-6 w-6 mr-3 text-blue-600" />
            Fleet Monitoring
          </h1>
          <p className="text-gray-600 mt-1">
            Real-time monitoring and tracking of your entire e-scooter fleet
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={`flex items-center px-3 py-2 rounded-lg text-sm ${
              autoRefresh
                ? "bg-green-100 text-green-700"
                : "bg-gray-100 text-gray-700"
            }`}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${autoRefresh ? "animate-spin" : ""}`} />
            Auto Refresh
          </button>
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Scooters ({stats.total})</option>
            <option value="in_use">In Use ({stats.active})</option>
            <option value="available">Available ({stats.available})</option>
            <option value="charging">Charging ({stats.charging})</option>
            <option value="maintenance">Maintenance ({stats.maintenance})</option>
            <option value="offline">Offline ({stats.offline})</option>
          </select>
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode("grid")}
              className={`px-3 py-1 rounded text-sm ${
                viewMode === "grid" ? "bg-white shadow-sm" : ""
              }`}
            >
              Grid
            </button>
            <button
              onClick={() => setViewMode("map")}
              className={`px-3 py-1 rounded text-sm ${
                viewMode === "map" ? "bg-white shadow-sm" : ""
              }`}
            >
              Map
            </button>
            <button
              onClick={() => setViewMode("list")}
              className={`px-3 py-1 rounded text-sm ${
                viewMode === "list" ? "bg-white shadow-sm" : ""
              }`}
            >
              List
            </button>
          </div>
        </div>
      </div>

      {/* Fleet Overview Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 text-center"
        >
          <p className="text-2xl font-bold text-blue-600">{stats.total}</p>
          <p className="text-xs text-gray-600">Total Fleet</p>
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 text-center"
        >
          <p className="text-2xl font-bold text-green-600">{stats.active}</p>
          <p className="text-xs text-gray-600">In Use</p>
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 text-center"
        >
          <p className="text-2xl font-bold text-blue-600">{stats.available}</p>
          <p className="text-xs text-gray-600">Available</p>
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 text-center"
        >
          <p className="text-2xl font-bold text-yellow-600">{stats.charging}</p>
          <p className="text-xs text-gray-600">Charging</p>
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 text-center"
        >
          <p className="text-2xl font-bold text-orange-600">{stats.maintenance}</p>
          <p className="text-xs text-gray-600">Maintenance</p>
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 text-center"
        >
          <p className="text-2xl font-bold text-red-600">{stats.offline}</p>
          <p className="text-xs text-gray-600">Offline</p>
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-white p-4 rounded-lg shadow-sm border border-gray-200 text-center"
        >
          <p className={`text-2xl font-bold ${getBatteryColor(stats.avgBattery)}`}>
            {stats.avgBattery}%
          </p>
          <p className="text-xs text-gray-600">Avg Battery</p>
        </motion.div>
      </div>

      {/* Fleet Grid/List View */}
      {viewMode === "grid" && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredFleet.map((scooter, index) => (
            <motion.div
              key={scooter.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow"
            >
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">{scooter.id}</h3>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(scooter.status)}
                  <span
                    className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
                      scooter.status
                    )}`}
                  >
                    {scooter.status.replace("_", " ")}
                  </span>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Battery</span>
                  <div className="flex items-center space-x-2">
                    <Battery className={`h-4 w-4 ${getBatteryColor(scooter.battery)}`} />
                    <span className={`font-medium ${getBatteryColor(scooter.battery)}`}>
                      {scooter.battery}%
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Location</span>
                  <div className="flex items-center space-x-1">
                    <MapPin className="h-4 w-4 text-gray-400" />
                    <span className="text-sm text-gray-900">{scooter.location.name}</span>
                  </div>
                </div>

                {scooter.status === "in_use" && (
                  <>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Rider</span>
                      <span className="text-sm text-gray-900">{scooter.rider}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Speed</span>
                      <span className="text-sm text-gray-900">{scooter.speed} km/h</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Ride Time</span>
                      <span className="text-sm text-gray-900">{scooter.rideTime}</span>
                    </div>
                  </>
                )}

                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Health</span>
                  <span className={`text-sm font-medium ${getHealthColor(scooter.health)}`}>
                    {scooter.health}
                  </span>
                </div>

                <div className="flex items-center justify-between text-xs text-gray-500">
                  <Clock className="h-3 w-3" />
                  <span>Updated {scooter.lastUpdate}</span>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      )}

      {viewMode === "map" && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="h-96 bg-gray-100 rounded-lg flex items-center justify-center">
            <div className="text-center">
              <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Interactive Fleet Map</h3>
              <p className="text-gray-600">
                Real-time map view showing all scooter locations and status
              </p>
            </div>
          </div>
        </div>
      )}

      {viewMode === "list" && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Scooter ID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Battery
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Location
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Health
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Update
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredFleet.map((scooter) => (
                  <tr key={scooter.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {scooter.id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                          scooter.status
                        )}`}
                      >
                        {scooter.status.replace("_", " ")}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <Battery className={`h-4 w-4 mr-2 ${getBatteryColor(scooter.battery)}`} />
                        <span className={`text-sm ${getBatteryColor(scooter.battery)}`}>
                          {scooter.battery}%
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {scooter.location.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`text-sm font-medium ${getHealthColor(scooter.health)}`}>
                        {scooter.health}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {scooter.lastUpdate}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default FleetMonitoring;
