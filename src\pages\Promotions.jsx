// src/pages/Promotions.jsx
import React, { useState } from "react";
import { motion } from "framer-motion";
import {
  Tag,
  Plus,
  Calendar,
  Users,
  DollarSign,
  TrendingUp,
  Edit,
  Trash2,
  Copy,
  Play,
  Pause,
} from "lucide-react";

const Promotions = () => {
  const [activeTab, setActiveTab] = useState("active");

  // Mock promotions data
  const promotions = [
    {
      id: 1,
      name: "New User Welcome",
      code: "WELCOME20",
      type: "percentage",
      value: 20,
      status: "active",
      used: 145,
      limit: 500,
      startDate: "2024-01-01",
      endDate: "2024-03-31",
      revenue: 2850,
    },
    {
      id: 2,
      name: "Weekend Special",
      code: "WEEKEND15",
      type: "percentage",
      value: 15,
      status: "active",
      used: 89,
      limit: 200,
      startDate: "2024-01-15",
      endDate: "2024-02-15",
      revenue: 1245,
    },
    {
      id: 3,
      name: "Student Discount",
      code: "STUDENT10",
      type: "fixed",
      value: 5,
      status: "paused",
      used: 67,
      limit: 1000,
      startDate: "2024-01-01",
      endDate: "2024-12-31",
      revenue: 890,
    },
  ];

  const tabs = [
    { id: "active", label: "Active Promotions", count: 2 },
    { id: "scheduled", label: "Scheduled", count: 3 },
    { id: "expired", label: "Expired", count: 8 },
    { id: "analytics", label: "Analytics", count: null },
  ];

  const getStatusColor = (status) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800";
      case "paused":
        return "bg-yellow-100 text-yellow-800";
      case "expired":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-blue-100 text-blue-800";
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <Tag className="h-6 w-6 mr-3 text-purple-600" />
            Promotions Management
          </h1>
          <p className="text-gray-600 mt-1">
            Create and manage promotional campaigns to boost user engagement
          </p>
        </div>
        <button className="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
          <Plus className="h-4 w-4 mr-2" />
          Create Promotion
        </button>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Active Promotions</p>
              <p className="text-2xl font-bold text-green-600">5</p>
              <p className="text-xs text-green-600 mt-1">2 ending soon</p>
            </div>
            <Tag className="h-8 w-8 text-green-600" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Redemptions</p>
              <p className="text-2xl font-bold text-blue-600">1,234</p>
              <p className="text-xs text-blue-600 mt-1">+15% this month</p>
            </div>
            <Users className="h-8 w-8 text-blue-600" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Revenue Impact</p>
              <p className="text-2xl font-bold text-purple-600">$12,450</p>
              <p className="text-xs text-purple-600 mt-1">Generated revenue</p>
            </div>
            <DollarSign className="h-8 w-8 text-purple-600" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Conversion Rate</p>
              <p className="text-2xl font-bold text-orange-600">24.5%</p>
              <p className="text-xs text-orange-600 mt-1">+3.2% vs last month</p>
            </div>
            <TrendingUp className="h-8 w-8 text-orange-600" />
          </div>
        </motion.div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? "border-purple-500 text-purple-600"
                    : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
              >
                {tab.label}
                {tab.count !== null && (
                  <span className="ml-2 bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                    {tab.count}
                  </span>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === "active" && (
            <div className="space-y-4">
              {promotions
                .filter((promo) => promo.status === "active")
                .map((promotion) => (
                  <motion.div
                    key={promotion.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <h3 className="text-lg font-semibold text-gray-900">
                            {promotion.name}
                          </h3>
                          <span
                            className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
                              promotion.status
                            )}`}
                          >
                            {promotion.status}
                          </span>
                          <span className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm font-mono">
                            {promotion.code}
                          </span>
                        </div>
                        <div className="mt-2 grid grid-cols-4 gap-4 text-sm text-gray-600">
                          <div>
                            <span className="font-medium">Discount:</span>{" "}
                            {promotion.type === "percentage"
                              ? `${promotion.value}%`
                              : `$${promotion.value}`}
                          </div>
                          <div>
                            <span className="font-medium">Used:</span>{" "}
                            {promotion.used}/{promotion.limit}
                          </div>
                          <div>
                            <span className="font-medium">Revenue:</span> $
                            {promotion.revenue.toLocaleString()}
                          </div>
                          <div>
                            <span className="font-medium">Expires:</span>{" "}
                            {new Date(promotion.endDate).toLocaleDateString()}
                          </div>
                        </div>
                        <div className="mt-3">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-purple-600 h-2 rounded-full"
                              style={{
                                width: `${(promotion.used / promotion.limit) * 100}%`,
                              }}
                            />
                          </div>
                          <p className="text-xs text-gray-500 mt-1">
                            {Math.round((promotion.used / promotion.limit) * 100)}% of limit used
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 ml-6">
                        <button className="p-2 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded-lg">
                          <Edit className="h-4 w-4" />
                        </button>
                        <button className="p-2 text-gray-600 hover:text-green-600 hover:bg-green-50 rounded-lg">
                          <Copy className="h-4 w-4" />
                        </button>
                        <button className="p-2 text-gray-600 hover:text-yellow-600 hover:bg-yellow-50 rounded-lg">
                          <Pause className="h-4 w-4" />
                        </button>
                        <button className="p-2 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg">
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </motion.div>
                ))}
            </div>
          )}

          {activeTab === "analytics" && (
            <div className="text-center py-12">
              <Tag className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Promotion Analytics
              </h3>
              <p className="text-gray-600">
                Detailed analytics and performance metrics for your promotional campaigns
              </p>
            </div>
          )}

          {(activeTab === "scheduled" || activeTab === "expired") && (
            <div className="text-center py-12">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {activeTab === "scheduled" ? "Scheduled Promotions" : "Expired Promotions"}
              </h3>
              <p className="text-gray-600">
                {activeTab === "scheduled"
                  ? "Promotions scheduled for future activation"
                  : "Historical promotion data and performance"}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Promotions;
