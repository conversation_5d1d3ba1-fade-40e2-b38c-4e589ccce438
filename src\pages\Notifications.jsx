// src/pages/Notifications.jsx
import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  Bell,
  Plus,
  Send,
  Users,
  MessageSquare,
  Calendar,
  Target,
  Eye,
  Edit,
  Trash2,
  Filter,
  Search,
} from "lucide-react";
import NotificationForm from "../components/notifications/NotificationForm";
import NotificationList from "../components/notifications/NotificationList";
import NotificationStats from "../components/notifications/NotificationStats";
import NotificationTemplates from "../components/notifications/NotificationTemplates";

const Notifications = () => {
  const [activeTab, setActiveTab] = useState("list");
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedNotification, setSelectedNotification] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");

  const tabs = [
    { id: "list", label: "All Notifications", icon: Bell },
    { id: "stats", label: "Analytics", icon: Target },
    { id: "templates", label: "Templates", icon: MessageSquare },
  ];

  const handleCreateNotification = () => {
    setSelectedNotification(null);
    setIsFormOpen(true);
  };

  const handleEditNotification = (notification) => {
    setSelectedNotification(notification);
    setIsFormOpen(true);
  };

  const handleFormClose = () => {
    setIsFormOpen(false);
    setSelectedNotification(null);
  };

  const handleFormSubmit = (notificationData) => {
    // Handle form submission
    console.log("Notification data:", notificationData);
    setIsFormOpen(false);
    setSelectedNotification(null);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <Bell className="h-6 w-6 mr-3 text-orange-600" />
            Notifications Management
          </h1>
          <p className="text-gray-600 mt-1">
            Send targeted notifications and manage communication with your users
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="relative">
            <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search notifications..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
            />
          </div>
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
          >
            <option value="all">All Status</option>
            <option value="sent">Sent</option>
            <option value="scheduled">Scheduled</option>
            <option value="draft">Draft</option>
            <option value="failed">Failed</option>
          </select>
          <button
            onClick={handleCreateNotification}
            className="flex items-center px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Notification
          </button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Sent</p>
              <p className="text-2xl font-bold text-green-600">12,456</p>
              <p className="text-xs text-green-600 mt-1">+8.5% this month</p>
            </div>
            <Send className="h-8 w-8 text-green-600" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Open Rate</p>
              <p className="text-2xl font-bold text-blue-600">68.5%</p>
              <p className="text-xs text-blue-600 mt-1">+2.3% vs last month</p>
            </div>
            <Eye className="h-8 w-8 text-blue-600" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Active Users</p>
              <p className="text-2xl font-bold text-purple-600">3,247</p>
              <p className="text-xs text-purple-600 mt-1">Notification enabled</p>
            </div>
            <Users className="h-8 w-8 text-purple-600" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white p-6 rounded-lg shadow-sm border border-gray-200"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Scheduled</p>
              <p className="text-2xl font-bold text-orange-600">15</p>
              <p className="text-xs text-orange-600 mt-1">Next 7 days</p>
            </div>
            <Calendar className="h-8 w-8 text-orange-600" />
          </div>
        </motion.div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? "border-orange-500 text-orange-600"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === "list" && (
            <NotificationList
              searchTerm={searchTerm}
              filterStatus={filterStatus}
              onEdit={handleEditNotification}
            />
          )}
          {activeTab === "stats" && <NotificationStats />}
          {activeTab === "templates" && <NotificationTemplates />}
        </div>
      </div>

      {/* Notification Form Modal */}
      <AnimatePresence>
        {isFormOpen && (
          <NotificationForm
            notification={selectedNotification}
            onClose={handleFormClose}
            onSubmit={handleFormSubmit}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default Notifications;
