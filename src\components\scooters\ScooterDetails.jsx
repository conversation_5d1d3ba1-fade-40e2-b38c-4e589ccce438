// src/components/scooters/ScooterDetails.jsx
import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  Battery,
  MapPin,
  Calendar,
  Clock,
  Activity,
  AlertTriangle,
  ChevronLeft,
  Settings,
  Lock,
  Unlock,
  Loader,
} from "lucide-react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { scooterService } from "../../services/api";

const ScooterDetails = ({ scooter, onBack }) => {
  const queryClient = useQueryClient();
  const scooterId = scooter?.id || scooter?._id;

  // Fetch detailed scooter data
  const {
    data: scooterDetails,
    isLoading: isLoadingDetails,
    error: detailsError,
  } = useQuery({
    queryKey: ["scooter", scooterId],
    queryFn: () =>
      scooterService
        .getScooterById(scooterId)
        .then((response) => response.data),
    enabled: !!scooterId,
  });

  // Fetch telemetry data
  const { data: telemetry, isLoading: isLoadingTelemetry } = useQuery({
    queryKey: ["telemetry", scooterId],
    queryFn: () =>
      scooterService
        .getLatestTelemetry(scooterId)
        .then((response) => response.data),
    enabled: !!scooterId,
  });

  // Get maintenance history
  const [maintenanceHistory, setMaintenanceHistory] = useState([
    {
      id: 1,
      date: "2024-01-15",
      type: "Regular Maintenance",
      description: "Battery check, brake adjustment",
      technician: "John Smith",
    },
    {
      id: 2,
      date: "2024-01-01",
      type: "Repair",
      description: "Replace front wheel",
      technician: "Mike Johnson",
    },
  ]);

  // Get ride history
  const { data: rides, isLoading: isLoadingRides } = useQuery({
    queryKey: ["rides", scooterId],
    queryFn: () =>
      scooterService
        .getAllRides({ scooterId })
        .then((response) => response.data?.slice(0, 5) || []), // Get latest 5 rides
    enabled: !!scooterId,
  });

  // Prepare ride history from the API data
  const rideHistory = rides || [
    {
      id: 1,
      date: "2024-02-01",
      duration: "25 mins",
      distance: "3.5 km",
      user: "User #12345",
    },
    {
      id: 2,
      date: "2024-01-31",
      duration: "15 mins",
      distance: "2.1 km",
      user: "User #12346",
    },
  ];

  // Mutations for scooter actions
  const updateStatusMutation = useMutation({
    mutationFn: (status) =>
      scooterService.updateScooterStatus(scooterId, status),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["scooter", scooterId] });
      queryClient.invalidateQueries({ queryKey: ["scooters"] });
    },
  });

  const getBatteryColor = (level) => {
    if (level > 70) return "text-green-500";
    if (level > 30) return "text-yellow-500";
    return "text-red-500";
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-4">
          <button onClick={onBack} className="p-2 hover:bg-gray-100 rounded-lg">
            <ChevronLeft className="w-5 h-5" />
          </button>
          <h2 className="text-xl font-semibold text-gray-800">
            Scooter Details -{" "}
            {scooterDetails?.name ||
              scooter?.name ||
              scooter?.id ||
              "Loading..."}
          </h2>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Status Card */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">
            Current Status
          </h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center text-gray-600">
                <Battery
                  className={`w-5 h-5 mr-2 ${getBatteryColor(
                    scooterDetails?.batteryLevel ||
                      scooter?.batteryLevel ||
                      scooter?.battery ||
                      0
                  )}`}
                />
                Battery Level
              </div>
              <span className="font-medium">
                {scooterDetails?.batteryLevel ||
                  scooter?.batteryLevel ||
                  scooter?.battery ||
                  0}
                %
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center text-gray-600">
                <MapPin className="w-5 h-5 mr-2" />
                Location
              </div>
              <span className="font-medium">
                {scooterDetails?.lastStation ||
                  scooter?.lastStation ||
                  scooter?.location ||
                  "Unknown"}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center text-gray-600">
                <Activity className="w-5 h-5 mr-2" />
                Status
              </div>
              <span className="font-medium capitalize">
                {scooterDetails?.status || scooter?.status || "Unknown"}
              </span>
            </div>
            <div className="pt-4 flex space-x-2">
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => updateStatusMutation.mutate("maintenance")}
                className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-gray-200 rounded-lg hover:bg-gray-50">
                <Lock className="w-4 h-4 mr-2" />
                Set to Maintenance
              </motion.button>
              <motion.button
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => updateStatusMutation.mutate("available")}
                className="flex-1 inline-flex items-center justify-center px-4 py-2 border border-gray-200 rounded-lg hover:bg-gray-50">
                <Settings className="w-4 h-4 mr-2" />
                Set to Available
              </motion.button>
            </div>
          </div>
        </motion.div>

        {/* Maintenance History */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800">
              Maintenance History
            </h3>
            <button className="text-sm text-teal-600 hover:text-teal-700">
              View All
            </button>
          </div>
          <div className="space-y-4">
            {maintenanceHistory.map((record) => (
              <div key={record.id} className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-gray-800">
                    {record.type}
                  </span>
                  <span className="text-sm text-gray-500">
                    {new Date(record.date).toLocaleDateString()}
                  </span>
                </div>
                <p className="text-sm text-gray-600">{record.description}</p>
                <p className="text-sm text-gray-500 mt-2">
                  Technician: {record.technician}
                </p>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Ride History */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800">
              Recent Rides
            </h3>
            <button className="text-sm text-teal-600 hover:text-teal-700">
              View All
            </button>
          </div>
          <div className="space-y-4">
            {rideHistory.map((ride) => (
              <div key={ride.id} className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium text-gray-800">{ride.user}</span>
                  <span className="text-sm text-gray-500">
                    {new Date(ride.date).toLocaleDateString()}
                  </span>
                </div>
                <div className="flex items-center justify-between text-sm text-gray-600">
                  <div className="flex items-center">
                    <Clock className="w-4 h-4 mr-1" />
                    {ride.duration}
                  </div>
                  <div className="flex items-center">
                    <MapPin className="w-4 h-4 mr-1" />
                    {ride.distance}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Map Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">Location</h3>
        <div className="h-96 bg-gray-100 rounded-lg">
          {/* Map component will be integrated here */}
          <div className="w-full h-full flex items-center justify-center text-gray-500">
            Map View
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default ScooterDetails;
