{"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "create": "Create", "add": "Add", "search": "Search", "filter": "Filter", "refresh": "Refresh", "export": "Export", "import": "Import", "view": "View", "close": "Close", "submit": "Submit", "back": "Back", "next": "Next", "previous": "Previous", "yes": "Yes", "no": "No", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "unknown": "Unknown", "all": "All", "none": "None", "total": "Total", "status": "Status", "type": "Type", "name": "Name", "description": "Description", "location": "Location", "date": "Date", "time": "Time", "actions": "Actions"}, "navigation": {"dashboard": "Dashboard", "users": "Users", "scooters": "Scooters", "bookings": "Bookings", "reports": "Reports & Analytics", "operations": "Operations", "fleetMonitoring": "Fleet Monitoring", "geofencing": "Geofencing", "incidents": "Incidents", "marketing": "Marketing", "notifications": "Notifications", "promotions": "Promotions", "settings": "Settings", "logout": "Logout"}, "header": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "profile": "Profile", "adminUser": "Admin User"}, "login": {"title": "<PERSON><PERSON>er Admin Panel", "subtitle": "Sign in to your account", "email": "Email address", "password": "Password", "signIn": "Sign in", "signingIn": "Signing in..."}, "dashboard": {"title": "Dashboard", "subtitle": "Real-time overview of your e-scooter fleet", "totalRevenue": "Total Revenue", "activeScooters": "Active Scooters", "activeUsers": "Active Users", "activeRides": "Active Rides", "ridesCompleted": "rides completed", "currentlyInUse": "currently in use", "totalRegistered": "total registered", "currentlyOngoing": "Currently ongoing", "lastHour": "Last Hour", "last24Hours": "Last 24 Hours", "last7Days": "Last 7 Days", "last30Days": "Last 30 Days", "showApiPanel": "Show API Panel", "hideApiPanel": "Hide API Panel", "vsLastPeriod": "vs last period", "heatmap": {"title": "Usage Heatmap", "subtitle": "Scooter usage patterns and hotspots", "byLocation": "By Location", "byTime": "By Time", "combinedView": "Combined View", "locationBasedUsage": "Location-based Usage", "timeBasedUsage": "Time-based Usage", "topUsageZones": "Top Usage Zones", "peakHours": "Peak Hours", "usageIntensity": "Usage Intensity", "low": "Low", "high": "High", "rides": "rides", "usage": "usage", "downtown": "Downtown", "university": "University", "businessDistrict": "Business District", "residentialNorth": "Residential North", "shoppingMall": "Shopping Mall", "airport": "Airport", "beachArea": "Beach Area", "industrial": "Industrial"}, "revenueChart": {"title": "Revenue Trends", "subtitle": "Revenue performance over {{timeRange}}", "rides": "rides", "avg": "avg", "peakHour": "Peak Hour", "growthRate": "Growth Rate", "efficiency": "Efficiency", "period": "period", "revenue": "Revenue", "avgRide": "Avg/Ride"}}, "users": {"title": "Users", "addNewUser": "Add New User", "searchUsers": "Search users...", "allStatuses": "All Statuses", "userDetails": "User Details", "editUser": "Edit User", "createUser": "Create User", "firstName": "First Name", "lastName": "Last Name", "email": "Email", "phone": "Phone", "role": "Role", "joinDate": "Join Date", "lastActive": "Last Active", "totalRides": "Total Rides", "showingUsers": "Showing {{count}} users", "noUsersFound": "No users found"}, "scooters": {"title": "Scooters", "addNewScooter": "Add <PERSON> Scooter", "searchScooters": "Search scooters...", "filters": "Filters", "minBattery": "Min Battery", "anyBattery": "Any Battery", "atLeast20": "At least 20%", "atLeast50": "At least 50%", "atLeast80": "At least 80%", "clearFilters": "Clear Filters", "available": "Available", "inUse": "In Use", "maintenance": "Maintenance", "offline": "Offline", "battery": "Battery", "model": "Model", "standard": "Standard", "lastMaintenance": "Last Maintenance", "rides": "rides", "showingScooters": "Showing {{count}} scooters", "errorLoading": "Error loading scooters. Please try again.", "noScootersFound": "No scooters found"}, "geofencing": {"title": "Geofencing Management", "subtitle": "Define and manage geofenced areas for your e-scooter operations", "createZone": "Create Zone", "totalZones": "Total Zones", "scootersInZones": "Scooters in Zones", "currentlyTracked": "Currently tracked", "restrictedZones": "Restricted Zones", "activeRestrictions": "Active restrictions", "parkingZones": "Parking Zones", "parkingAllowed": "Parking allowed", "geofenceZones": "Geofence Zones", "interactiveMap": "Interactive Map", "zoneAnalytics": "Zone Analytics", "globalSettings": "Global Settings", "allowed": "Allowed", "restricted": "Restricted", "noRide": "No-Ride", "parkingOnly": "Parking Only", "coordinates": "Coordinates", "radius": "<PERSON><PERSON>", "maxSpeed": "Max Speed", "scootersInside": "Scooters Inside", "rules": "Rules", "forbidden": "Forbidden", "createGeofenceZone": "Create Geofence Zone", "zoneName": "Zone Name", "zoneType": "Zone Type", "allowedZone": "Allowed Zone", "restrictedZone": "Restricted Zone", "noRideZone": "No-Ride Zone", "radiusMeters": "Radius (meters)", "maxSpeedKmh": "Max Speed (km/h)", "timeRestrictions": "Time Restrictions", "assignedScooters": "Assigned Scooters (Optional)", "assignedScootersPlaceholder": "Enter scooter IDs or tags separated by commas (e.g., SC-001, SC-002, tag:downtown)", "assignedScootersHelp": "Specify individual scooter IDs (SC-001) or use tags (tag:region-name) to assign scooters to this zone", "interactiveGeofencingMap": "Interactive Geofencing Map", "mapLegend": "Click on any zone circle to view detailed information. Map shows real-time zone boundaries and coverage areas.", "zoneViolations": "Zone Violations", "speedViolations": "Speed violations", "parkingViolations": "Parking violations", "noRideZoneEntries": "No-ride zone entries", "thisWeek": "this week", "zoneUsage": "Zone Usage", "mostActiveZone": "Most active zone", "averageDwellTime": "Average dwell time", "peakUsageHours": "Peak usage hours", "enableGeofencing": "Enable Geofencing", "enableGeofencingHelp": "Master switch for all geofencing features", "automaticSpeedLimiting": "Automatic Speed Limiting", "automaticSpeedLimitingHelp": "Automatically limit speed in restricted zones", "zoneViolationAlerts": "Zone Violation Alerts", "zoneViolationAlertsHelp": "Send alerts when scooters violate zone rules", "mapPopup": {"type": "Type", "status": "Status", "radius": "<PERSON><PERSON>", "maxSpeed": "Max Speed", "scooters": "Scooters", "parking": "Parking", "riding": "Riding", "meters": "m", "kmh": "km/h", "allowedText": "Allowed", "forbiddenText": "Forbidden"}}}