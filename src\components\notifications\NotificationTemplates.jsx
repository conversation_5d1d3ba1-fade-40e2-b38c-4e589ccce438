// src/components/notifications/NotificationTemplates.jsx
import React from "react";
import { motion } from "framer-motion";
import { MessageSquare, Plus } from "lucide-react";

const NotificationTemplates = () => {
  return (
    <div className="space-y-6">
      <div className="text-center py-12">
        <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Notification Templates</h3>
        <p className="text-gray-600">
          Pre-built templates for common notification types
        </p>
      </div>
    </div>
  );
};

export default NotificationTemplates;
