@import "tailwindcss";

/* Leaflet CSS for maps */
@import 'leaflet/dist/leaflet.css';

/* RTL Support */
[dir="rtl"] {
  direction: rtl;
}

[dir="ltr"] {
  direction: ltr;
}

/* RTL specific styles */
[dir="rtl"] .flex-row-reverse {
  flex-direction: row-reverse;
}

[dir="rtl"] .text-right {
  text-align: right;
}

[dir="rtl"] .text-left {
  text-align: left;
}

/* Fix for specific components in RTL */
[dir="rtl"] .ml-2 {
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .mr-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}

[dir="rtl"] .ml-4 {
  margin-left: 0;
  margin-right: 1rem;
}

[dir="rtl"] .mr-4 {
  margin-right: 0;
  margin-left: 1rem;
}
