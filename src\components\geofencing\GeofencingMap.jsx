import React, { useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>ile<PERSON><PERSON>er, Circle, Popup, useMap } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';

// Fix for default markers in react-leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Component to auto-fit bounds
const AutoFitBounds = ({ zones }) => {
  const map = useMap();

  useEffect(() => {
    if (zones.length > 0) {
      const bounds = L.latLngBounds();
      zones.forEach(zone => {
        const [lat, lng] = zone.coordinates.split(',').map(coord => parseFloat(coord.trim()));
        bounds.extend([lat, lng]);
      });
      
      // Add some padding around the bounds
      map.fitBounds(bounds, { padding: [20, 20] });
    } else {
      // Default to Riyadh center if no zones
      map.setView([24.7136, 46.6753], 11);
    }
  }, [map, zones]);

  return null;
};

const GeofencingMap = ({ zones }) => {
  // Filter zones to only include those in Riyadh area
  const riyadhZones = zones.filter(zone => {
    const [lat, lng] = zone.coordinates.split(',').map(coord => parseFloat(coord.trim()));
    // Check if coordinates are within Riyadh area (approximate bounds)
    // Riyadh is roughly between 24.4-25.0 latitude and 46.4-47.0 longitude
    return lat >= 24.4 && lat <= 25.0 && lng >= 46.4 && lng <= 47.0;
  });

  // Color mapping for zone types
  const getZoneColor = (type) => {
    switch (type) {
      case 'allowed':
        return '#10b981'; // green
      case 'restricted':
        return '#f59e0b'; // yellow
      case 'no-ride':
        return '#ef4444'; // red
      case 'parking-only':
        return '#3b82f6'; // blue
      default:
        return '#6b7280'; // gray
    }
  };

  // Get zone type display name
  const getZoneTypeName = (type) => {
    switch (type) {
      case 'allowed':
        return 'Allowed Zone';
      case 'restricted':
        return 'Restricted Zone';
      case 'no-ride':
        return 'No-Ride Zone';
      case 'parking-only':
        return 'Parking Only';
      default:
        return 'Unknown';
    }
  };

  return (
    <div className="h-96 w-full rounded-lg border border-gray-200 overflow-hidden" style={{ minHeight: '400px' }}>
      <MapContainer
        center={[24.7136, 46.6753]} // Riyadh center
        zoom={11}
        style={{ height: '100%', width: '100%' }}
        className="rounded-lg"
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />
        
        <AutoFitBounds zones={riyadhZones} />
        
        {riyadhZones.map((zone) => {
          const [lat, lng] = zone.coordinates.split(',').map(coord => parseFloat(coord.trim()));
          const color = getZoneColor(zone.type);
          
          return (
            <Circle
              key={zone.id}
              center={[lat, lng]}
              radius={zone.radius}
              pathOptions={{
                color: color,
                fillColor: color,
                fillOpacity: 0.3,
                weight: 2,
              }}
            >
              <Popup>
                <div className="p-2 min-w-[200px]">
                  <h3 className="font-semibold text-gray-900 mb-2">{zone.name}</h3>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Type:</span>
                      <span 
                        className="px-2 py-1 rounded text-xs font-medium"
                        style={{ 
                          backgroundColor: color + '20', 
                          color: color 
                        }}
                      >
                        {getZoneTypeName(zone.type)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Status:</span>
                      <span className={`px-2 py-1 rounded text-xs font-medium ${
                        zone.status === 'active' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {zone.status}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Radius:</span>
                      <span className="text-gray-900">{zone.radius}m</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Max Speed:</span>
                      <span className="text-gray-900">{zone.rules.maxSpeed} km/h</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Scooters:</span>
                      <span className="text-gray-900">{zone.scootersInside}</span>
                    </div>
                    <div className="mt-2 pt-2 border-t border-gray-200">
                      <div className="flex flex-wrap gap-1">
                        <span className={`px-2 py-1 rounded text-xs ${
                          zone.rules.parkingAllowed 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          Parking {zone.rules.parkingAllowed ? 'Allowed' : 'Forbidden'}
                        </span>
                        <span className={`px-2 py-1 rounded text-xs ${
                          zone.rules.ridingAllowed 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          Riding {zone.rules.ridingAllowed ? 'Allowed' : 'Forbidden'}
                        </span>
                      </div>
                      {zone.rules.timeRestrictions && (
                        <div className="mt-1">
                          <span className="px-2 py-1 rounded text-xs bg-blue-100 text-blue-800">
                            {zone.rules.timeRestrictions}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </Popup>
            </Circle>
          );
        })}
      </MapContainer>
    </div>
  );
};

export default GeofencingMap;
