// src/App.jsx
import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import Routes from "./routes";

function App() {
  const { i18n } = useTranslation();

  useEffect(() => {
    // Set document direction based on language
    const isRTL = i18n.language === 'ar';
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
    document.documentElement.lang = i18n.language;
  }, [i18n.language]);

  return <Routes />;
}

export default App;
