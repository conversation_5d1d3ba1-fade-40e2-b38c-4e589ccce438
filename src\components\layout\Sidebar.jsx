// src/components/layout/Sidebar.jsx
import React, { useState } from "react";
import { NavLink } from "react-router-dom";
import { motion } from "framer-motion";
import {
  LayoutDashboard,
  Users,
  Bike,
  Calendar,
  FileText,
  Bell,
  Tag,
  Activity,
  MapPin,
  AlertTriangle,
  Settings,
  LogOut,
  ChevronDown,
  ChevronRight
} from "lucide-react";

const menuItems = [
  { path: "/dashboard", icon: LayoutDashboard, label: "Dashboard" },
  { path: "/users", icon: Users, label: "Users" },
  { path: "/scooters", icon: Bike, label: "Scooters" },
  { path: "/bookings", icon: Calendar, label: "Bookings" },
  { path: "/reports", icon: FileText, label: "Reports & Analytics" },
  {
    label: "Operations",
    isGroup: true,
    items: [
      { path: "/fleet-monitoring", icon: Activity, label: "Fleet Monitoring" },
      { path: "/geofencing", icon: MapPin, label: "Geofencing" },
      { path: "/incidents", icon: AlertTriangle, label: "Incidents" },
    ]
  },
  {
    label: "Marketing",
    isGroup: true,
    items: [
      { path: "/notifications", icon: Bell, label: "Notifications" },
      { path: "/promotions", icon: Tag, label: "Promotions" },
    ]
  },
  { path: "/settings", icon: Settings, label: "Settings" },
];

const Sidebar = () => {
  const [expandedGroups, setExpandedGroups] = useState(new Set(["Operations", "Marketing"]));

  const toggleGroup = (groupLabel) => {
    const newExpanded = new Set(expandedGroups);
    if (newExpanded.has(groupLabel)) {
      newExpanded.delete(groupLabel);
    } else {
      newExpanded.add(groupLabel);
    }
    setExpandedGroups(newExpanded);
  };

  const MenuItem = ({ icon: Icon, label, path }) => {
    return (
      <NavLink
        to={path}
        className={({ isActive }) =>
          `flex items-center px-4 py-3 text-gray-600 hover:bg-teal-50 hover:text-teal-700 rounded-lg transition-colors duration-200 ${
            isActive ? "bg-teal-50 text-teal-700" : ""
          }`
        }>
        <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.95 }}>
          <Icon className="h-5 w-5" />
        </motion.div>
        <span className="ml-3">{label}</span>
      </NavLink>
    );
  };

  const GroupHeader = ({ label, isExpanded, onToggle }) => {
    return (
      <button
        onClick={onToggle}
        className="flex items-center justify-between w-full px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 transition-colors"
      >
        <span>{label}</span>
        <motion.div
          animate={{ rotate: isExpanded ? 90 : 0 }}
          transition={{ duration: 0.2 }}
        >
          <ChevronRight className="h-4 w-4" />
        </motion.div>
      </button>
    );
  };

  return (
    <div className="flex flex-col h-full w-64 bg-white border-r border-gray-200">
      <div className="flex items-center justify-center h-16 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <Bike className="h-8 w-8 text-teal-600" />
          <span className="text-xl font-bold text-gray-900">BarqScoot</span>
        </div>
      </div>

      <nav className="flex-1 overflow-y-auto p-4">
        <div className="space-y-1">
          {menuItems.map((item, index) => {
            if (item.isGroup) {
              const isExpanded = expandedGroups.has(item.label);
              return (
                <div key={item.label}>
                  <GroupHeader
                    label={item.label}
                    isExpanded={isExpanded}
                    onToggle={() => toggleGroup(item.label)}
                  />
                  {isExpanded && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      className="ml-4 space-y-1"
                    >
                      {item.items.map((subItem) => (
                        <MenuItem key={subItem.path} {...subItem} />
                      ))}
                    </motion.div>
                  )}
                </div>
              );
            }
            return <MenuItem key={item.path} {...item} />;
          })}
        </div>
      </nav>

      <div className="p-4 border-t border-gray-200">
        <button className="flex items-center px-4 py-3 text-gray-600 hover:bg-red-50 hover:text-red-700 rounded-lg transition-colors duration-200 w-full">
          <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.95 }}>
            <LogOut className="h-5 w-5" />
          </motion.div>
          <span className="ml-3">Logout</span>
        </button>
      </div>
    </div>
  );
};

export default Sidebar;
