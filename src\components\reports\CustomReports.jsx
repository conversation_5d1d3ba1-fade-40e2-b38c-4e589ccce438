// src/components/reports/CustomReports.jsx
import React, { useState } from "react";
import { motion } from "framer-motion";
import { BarChart3, Plus, Download, Filter } from "lucide-react";

const CustomReports = ({ dateRange }) => {
  return (
    <div className="space-y-6">
      <div className="text-center py-12">
        <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Custom Reports</h3>
        <p className="text-gray-600">
          Create and manage custom reports with flexible data filtering
        </p>
      </div>
    </div>
  );
};

export default CustomReports;
